const { PrismaClient } = require('@prisma/client')
const { SignJWT, jwtVerify } = require('jose')
require('dotenv').config()

const prisma = new PrismaClient()

// JWT Configuration (from auth.ts)
const JWT_SECRET = new TextEncoder().encode(process.env.JWT_SECRET || 'your-super-secret-jwt-key-change-this-in-production-make-it-very-long-and-random')
const JWT_ISSUER = 'pasabuy-local'
const JWT_AUDIENCE = 'pasabuy-users'

console.log('🔧 Environment check:')
console.log(`JWT_SECRET length: ${process.env.JWT_SECRET?.length || 0}`)
console.log(`DATABASE_URL: ${process.env.DATABASE_URL || 'Not set'}`)
console.log('')

async function testAuth() {
  try {
    console.log('🧪 Testing Authentication Flow...\n')
    
    // Get a recent active session
    const session = await prisma.userSession.findFirst({
      where: { isActive: true },
      include: {
        user: {
          include: {
            roles: {
              include: {
                role: {
                  include: {
                    permissions: {
                      include: {
                        permission: true
                      }
                    }
                  }
                }
              }
            }
          }
        }
      },
      orderBy: { createdAt: 'desc' }
    })
    
    if (!session) {
      console.log('❌ No active sessions found')
      return
    }
    
    console.log(`🔍 Testing session for user: ${session.user.email}`)
    console.log(`📅 Session expires: ${session.expiresAt}`)
    console.log(`🕒 Current time: ${new Date()}`)
    console.log(`✅ Session valid: ${session.expiresAt > new Date()}`)
    
    // Test JWT verification
    console.log('\n🔐 Testing JWT verification...')
    try {
      const { payload } = await jwtVerify(session.token, JWT_SECRET, {
        issuer: JWT_ISSUER,
        audience: JWT_AUDIENCE,
      })
      
      console.log('✅ JWT verification successful')
      console.log(`👤 User ID: ${payload.userId}`)
      console.log(`📧 Email: ${payload.email}`)
      console.log(`🎭 Roles: ${payload.roles?.join(', ') || 'None'}`)
      console.log(`🔑 Permissions: ${payload.permissions?.length || 0} permissions`)
      
    } catch (jwtError) {
      console.log('❌ JWT verification failed:', jwtError.message)
    }
    
    // Test API call simulation
    console.log('\n🌐 Simulating API call...')
    
    // Simulate the getUserFromRequest function
    const mockRequest = {
      headers: {
        get: (name) => {
          if (name === 'authorization') {
            return `Bearer ${session.token}`
          }
          return null
        }
      }
    }
    
    // Extract token
    const authHeader = mockRequest.headers.get('authorization')
    const token = authHeader?.replace('Bearer ', '')
    
    if (!token) {
      console.log('❌ No token found in authorization header')
      return
    }
    
    console.log(`🎫 Token extracted: ${token.substring(0, 20)}...`)
    
    // Verify token
    const payload = await jwtVerify(token, JWT_SECRET, {
      issuer: JWT_ISSUER,
      audience: JWT_AUDIENCE,
    })
    
    if (!payload) {
      console.log('❌ Token verification failed')
      return
    }
    
    console.log('✅ Token verification successful')
    
    // Verify session is still active
    const activeSession = await prisma.userSession.findUnique({
      where: { 
        id: payload.sessionId,
        isActive: true
      }
    })
    
    if (!activeSession || activeSession.expiresAt < new Date()) {
      console.log('❌ Session is not active or expired')
      return
    }
    
    console.log('✅ Session is active and valid')
    
    // Get user data
    const user = await prisma.user.findUnique({
      where: { id: payload.userId },
      include: {
        roles: {
          where: { isActive: true },
          include: {
            role: {
              include: {
                permissions: {
                  include: {
                    permission: true
                  }
                }
              }
            }
          }
        }
      }
    })
    
    if (!user) {
      console.log('❌ User not found')
      return
    }
    
    console.log('✅ User found and active')
    console.log(`👤 User: ${user.email}`)
    console.log(`🔄 Active: ${user.isActive}`)
    console.log(`📧 Email Verified: ${user.emailVerified}`)
    
    console.log('\n🎉 Authentication test completed successfully!')
    
  } catch (error) {
    console.error('❌ Authentication test failed:', error)
  } finally {
    await prisma.$disconnect()
  }
}

testAuth()

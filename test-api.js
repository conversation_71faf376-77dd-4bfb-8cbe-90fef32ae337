const http = require('http')

async function testAPI() {
  console.log('🧪 Testing API Authentication...\n')
  
  // Test 1: Login with admin credentials
  console.log('1️⃣ Testing login...')
  
  const loginData = JSON.stringify({
    email: '<EMAIL>',
    password: 'password'
  })
  
  const loginOptions = {
    hostname: 'localhost',
    port: 3001,
    path: '/api/auth/login',
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Content-Length': Buffer.byteLength(loginData)
    }
  }
  
  try {
    const loginResponse = await makeRequest(loginOptions, loginData)
    console.log('✅ Login response status:', loginResponse.statusCode)
    
    if (loginResponse.statusCode === 200) {
      const loginResult = JSON.parse(loginResponse.body)
      console.log('✅ Login successful')
      console.log('🎫 Token received:', loginResult.token?.substring(0, 20) + '...')
      
      // Test 2: Use token to access protected endpoint
      console.log('\n2️⃣ Testing protected endpoint with token...')
      
      const meOptions = {
        hostname: 'localhost',
        port: 3001,
        path: '/api/auth/me',
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${loginResult.token}`,
          'Content-Type': 'application/json'
        }
      }
      
      const meResponse = await makeRequest(meOptions)
      console.log('📊 /api/auth/me response status:', meResponse.statusCode)
      
      if (meResponse.statusCode === 200) {
        const meResult = JSON.parse(meResponse.body)
        console.log('✅ Protected endpoint access successful')
        console.log('👤 User:', meResult.user?.email)
        console.log('🎭 Roles:', meResult.user?.roles?.map(r => r.name).join(', '))
      } else {
        console.log('❌ Protected endpoint failed')
        console.log('📄 Response:', meResponse.body)
      }
      
      // Test 3: Test another protected endpoint
      console.log('\n3️⃣ Testing another protected endpoint...')
      
      const customersOptions = {
        hostname: 'localhost',
        port: 3001,
        path: '/api/customers',
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${loginResult.token}`,
          'Content-Type': 'application/json'
        }
      }
      
      const customersResponse = await makeRequest(customersOptions)
      console.log('📊 /api/customers response status:', customersResponse.statusCode)
      
      if (customersResponse.statusCode === 200) {
        console.log('✅ Customers endpoint access successful')
      } else {
        console.log('❌ Customers endpoint failed')
        console.log('📄 Response:', customersResponse.body)
      }
      
    } else {
      console.log('❌ Login failed')
      console.log('📄 Response:', loginResponse.body)
    }
    
  } catch (error) {
    console.error('❌ Test failed:', error.message)
  }
}

function makeRequest(options, data = null) {
  return new Promise((resolve, reject) => {
    const req = http.request(options, (res) => {
      let body = ''
      
      res.on('data', (chunk) => {
        body += chunk
      })
      
      res.on('end', () => {
        resolve({
          statusCode: res.statusCode,
          headers: res.headers,
          body: body
        })
      })
    })
    
    req.on('error', (error) => {
      reject(error)
    })
    
    if (data) {
      req.write(data)
    }
    
    req.end()
  })
}

testAPI()

'use client'

import { useAuth } from '@/contexts/auth-context'
import { useCallback } from 'react'

interface FetchOptions extends RequestInit {
  headers?: Record<string, string>
}

export function useAuthenticatedFetch() {
  const { token, isAuthenticated, refreshToken, logout } = useAuth()

  const authenticatedFetch = useCallback(async (
    url: string, 
    options: FetchOptions = {}
  ): Promise<Response> => {
    // Wait for authentication to be determined
    if (!token && isAuthenticated === undefined) {
      throw new Error('Authentication state not yet determined')
    }

    // If not authenticated, throw error
    if (!isAuthenticated || !token) {
      throw new Error('User not authenticated')
    }

    // Prepare headers with authentication
    const headers = {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${token}`,
      ...options.headers
    }

    // Make the request
    const response = await fetch(url, {
      ...options,
      headers
    })

    // Handle 401 errors by attempting token refresh
    if (response.status === 401) {
      console.log('Token expired, attempting refresh...')
      
      const refreshSuccess = await refreshToken()
      
      if (refreshSuccess) {
        // Retry the request with new token
        const newToken = localStorage.getItem('auth_token')
        if (newToken) {
          const retryHeaders = {
            ...headers,
            'Authorization': `Bearer ${newToken}`
          }
          
          return fetch(url, {
            ...options,
            headers: retryHeaders
          })
        }
      } else {
        // Refresh failed, logout user
        await logout()
        throw new Error('Authentication failed - please login again')
      }
    }

    return response
  }, [token, isAuthenticated, refreshToken, logout])

  return authenticatedFetch
}

// Helper hook for common API patterns
export function useAuthenticatedAPI() {
  const authenticatedFetch = useAuthenticatedFetch()

  const get = useCallback(async (url: string) => {
    const response = await authenticatedFetch(url, { method: 'GET' })
    if (!response.ok) {
      throw new Error(`GET ${url} failed: ${response.statusText}`)
    }
    return response.json()
  }, [authenticatedFetch])

  const post = useCallback(async (url: string, data: any) => {
    const response = await authenticatedFetch(url, {
      method: 'POST',
      body: JSON.stringify(data)
    })
    if (!response.ok) {
      throw new Error(`POST ${url} failed: ${response.statusText}`)
    }
    return response.json()
  }, [authenticatedFetch])

  const put = useCallback(async (url: string, data: any) => {
    const response = await authenticatedFetch(url, {
      method: 'PUT',
      body: JSON.stringify(data)
    })
    if (!response.ok) {
      throw new Error(`PUT ${url} failed: ${response.statusText}`)
    }
    return response.json()
  }, [authenticatedFetch])

  const del = useCallback(async (url: string) => {
    const response = await authenticatedFetch(url, { method: 'DELETE' })
    if (!response.ok) {
      throw new Error(`DELETE ${url} failed: ${response.statusText}`)
    }
    return response.json()
  }, [authenticatedFetch])

  return { get, post, put, delete: del, fetch: authenticatedFetch }
}

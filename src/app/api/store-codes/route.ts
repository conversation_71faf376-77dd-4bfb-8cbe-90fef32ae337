import { prisma } from '@/lib/db'
import { NextRequest, NextResponse } from 'next/server'
import { withAuth, AuthenticatedRequest } from '@/lib/auth-middleware'

async function handleGetStoreCodes(request: AuthenticatedRequest): Promise<NextResponse> {
  try {
    const storeCodes = await prisma.storeCode.findMany({
      include: {
        _count: {
          select: {
            orders: {
              where: {
                isBought: false
              }
            }
          }
        }
      },
      orderBy: {
        code: 'asc'
      }
    })

    return NextResponse.json(storeCodes)
  } catch (error) {
    console.error('Error fetching store codes:', error)
    return NextResponse.json(
      { error: 'Error fetching store codes' },
      { status: 500 }
    )
  }
}

async function handleCreateStoreCode(request: AuthenticatedRequest): Promise<NextResponse> {
  try {
    const body = await request.json()
    const { code, name } = body

    if (!code) {
      return NextResponse.json(
        { error: 'Store code is required' },
        { status: 400 }
      )
    }

    const storeCode = await prisma.storeCode.create({
      data: {
        code: code.toUpperCase(),
        name: name || null
      }
    })

    return NextResponse.json(storeCode, { status: 201 })
  } catch (error: unknown) {
    if (error && typeof error === 'object' && 'code' in error && error.code === 'P2002') {
      return NextResponse.json(
        { error: 'Store code already exists' },
        { status: 400 }
      )
    }

    console.error('Error creating store code:', error)
    return NextResponse.json(
      { error: 'Error creating store code' },
      { status: 500 }
    )
  }
}

// Apply middleware and export handlers
export const GET = withAuth(handleGetStoreCodes)
export const POST = withAuth(handleCreateStoreCode)

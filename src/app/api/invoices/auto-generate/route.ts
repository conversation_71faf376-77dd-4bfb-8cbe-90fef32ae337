import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/db'
import { withPermission, AuthenticatedRequest } from '@/lib/auth-middleware'

async function handleAutoGenerateInvoice(request: AuthenticatedRequest): Promise<NextResponse> {
  try {
    const body = await request.json()
    const { customerId, triggerType = 'manual' } = body

    if (!customerId) {
      return NextResponse.json(
        { error: 'Customer ID is required' },
        { status: 400 }
      )
    }

    // Find all packed orders for this customer that are not already in invoices
    const packedOrders = await prisma.order.findMany({
      where: {
        customerId: parseInt(customerId),
        userId: request.user!.id, // Ensure user owns these orders
        isBought: true,
        packingStatus: 'Packed',
        // Exclude orders that are already in invoices
        NOT: {
          invoiceItems: {
            some: {}
          }
        }
      },
      include: {
        storeCode: true,
        customer: true
      }
    })

    if (packedOrders.length === 0) {
      return NextResponse.json({
        success: true,
        message: 'No packed orders available for invoice generation',
        invoice: null,
        orderCount: 0
      })
    }

    // Generate invoice number
    const invoiceCount = await prisma.invoice.count()
    const invoiceNumber = `INV-${String(invoiceCount + 1).padStart(6, '0')}`

    // Calculate totals
    let subtotal = 0
    const invoiceItemsData = packedOrders.map((order) => {
      const quantity = order.quantity
      const unitPrice = order.customerPrice
      const totalPrice = quantity * unitPrice

      subtotal += totalPrice

      return {
        orderId: order.id,
        quantity,
        unitPrice,
        totalPrice
      }
    })

    const total = subtotal

    // Set due date to 30 days from now
    const dueDate = new Date()
    dueDate.setDate(dueDate.getDate() + 30)

    // Create auto-generated note
    const autoNote = triggerType === 'automatic'
      ? 'Auto-generated from packed orders'
      : 'Generated from packed orders'

    // Create invoice with invoice items in a transaction
    const invoice = await prisma.$transaction(async (tx) => {
      const newInvoice = await tx.invoice.create({
        data: {
          invoiceNumber,
          customerId: parseInt(customerId),
          userId: request.user!.id, // Associate invoice with user
          status: 'DRAFT',
          subtotal,
          total,
          dueDate,
          notes: autoNote,
          invoiceItems: {
            create: invoiceItemsData
          }
        },
        include: {
          customer: true,
          invoiceItems: {
            include: {
              order: {
                include: {
                  storeCode: true
                }
              }
            }
          }
        }
      })

      return newInvoice
    })

    console.log(`Auto-generated invoice ${invoiceNumber} for customer ${invoice.customer?.name} with ${packedOrders.length} orders`)

    return NextResponse.json({
      success: true,
      message: `Successfully created invoice ${invoiceNumber} with ${packedOrders.length} orders`,
      invoice,
      orderCount: packedOrders.length,
      triggerType
    }, { status: 201 })

  } catch (error) {
    console.error('Error in auto-generating invoice:', error)
    return NextResponse.json(
      { error: 'Error auto-generating invoice' },
      { status: 500 }
    )
  }
}

// GET endpoint to check if auto-generation is possible for a customer
async function handleCheckAutoGeneration(request: AuthenticatedRequest): Promise<NextResponse> {
  try {
    const { searchParams } = new URL(request.url)
    const customerId = searchParams.get('customerId')

    if (!customerId) {
      return NextResponse.json(
        { error: 'Customer ID is required' },
        { status: 400 }
      )
    }

    // Count packed orders that are not already in invoices (user-scoped)
    const packedOrdersCount = await prisma.order.count({
      where: {
        customerId: parseInt(customerId),
        userId: request.user!.id, // Ensure user owns these orders
        isBought: true,
        packingStatus: 'Packed',
        NOT: {
          invoiceItems: {
            some: {}
          }
        }
      }
    })

    // Get customer info (user-scoped)
    const customer = await prisma.customer.findUnique({
      where: {
        id: parseInt(customerId),
        userId: request.user!.id // Ensure user owns this customer
      }
    })

    return NextResponse.json({
      customerId: parseInt(customerId),
      customerName: customer?.name || 'Unknown',
      packedOrdersCount,
      canGenerate: packedOrdersCount > 0
    })

  } catch (error) {
    console.error('Error checking auto-generation status:', error)
    return NextResponse.json(
      { error: 'Error checking auto-generation status' },
      { status: 500 }
    )
  }
}

// Apply middleware and export handlers
const postHandler = withPermission(
  { resource: 'invoices', action: 'create' },
  handleAutoGenerateInvoice
)

const getHandler = withPermission(
  { resource: 'invoices', action: 'read' },
  handleCheckAutoGeneration
)

export { postHandler as POST, getHandler as GET }

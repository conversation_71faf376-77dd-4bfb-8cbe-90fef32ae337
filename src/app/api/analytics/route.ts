import { NextRequest, NextResponse } from 'next/server'
import { OrderAnalyticsService } from '@/lib/order-analytics'
import { withAuth, AuthenticatedRequest } from '@/lib/auth-middleware'

async function handleAnalytics(request: AuthenticatedRequest): Promise<NextResponse> {
  try {
    const { searchParams } = new URL(request.url)
    const type = searchParams.get('type')
    const fromDate = searchParams.get('from')
    const toDate = searchParams.get('to')
    const interval = searchParams.get('interval') as 'day' | 'week' | 'month' || 'day'

    let dateRange: { from: Date; to: Date } | undefined
    if (fromDate && toDate) {
      dateRange = {
        from: new Date(fromDate),
        to: new Date(toDate)
      }
    }

    switch (type) {
      case 'performance':
        const performanceMetrics = await OrderAnalyticsService.getPerformanceMetrics(dateRange)
        return NextResponse.json(performanceMetrics)

      case 'status_distribution':
        const statusDistribution = await OrderAnalyticsService.getStatusDistribution(dateRange)
        return NextResponse.json(statusDistribution)

      case 'trends':
        if (!dateRange) {
          return NextResponse.json(
            { error: 'Date range required for trend analysis' },
            { status: 400 }
          )
        }
        const trendData = await OrderAnalyticsService.getTrendData(dateRange, interval)
        return NextResponse.json(trendData)

      case 'bottlenecks':
        const bottleneckAnalysis = await OrderAnalyticsService.getBottleneckAnalysis(dateRange)
        return NextResponse.json(bottleneckAnalysis)

      case 'customer_performance':
        const customerPerformance = await OrderAnalyticsService.getCustomerPerformance(dateRange)
        return NextResponse.json(customerPerformance)

      case 'dashboard':
        const dashboardMetrics = await OrderAnalyticsService.getDashboardMetrics()
        return NextResponse.json(dashboardMetrics)

      default:
        return NextResponse.json(
          { error: 'Invalid analytics type. Use: performance, status_distribution, trends, bottlenecks, customer_performance, or dashboard' },
          { status: 400 }
        )
    }
  } catch (error) {
    console.error('Error fetching analytics:', error)
    return NextResponse.json(
      { error: 'Error fetching analytics data' },
      { status: 500 }
    )
  }
}

// Apply middleware and export handler
export const GET = withAuth(handleAnalytics)

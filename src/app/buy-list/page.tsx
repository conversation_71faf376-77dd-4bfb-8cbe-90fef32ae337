'use client'

import { useEffect, useState } from 'react'
import { Card } from '@/components/ui/card'
import { SimplePageWrapper } from '@/components/layout/page-wrapper'
import { ProtectedRoute } from '@/components/auth/protected-route'
import Link from 'next/link'

type StoreCodeWithCount = {
  id: number
  code: string
  name: string | null
  createdAt: Date
  _count: {
    orders: number
  }
}

export default function BuyListPage() {
  const [storeCodes, setStoreCodes] = useState<StoreCodeWithCount[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    const fetchStoreCodes = async () => {
      try {
        setIsLoading(true)
        setError(null)

        const response = await fetch('/api/store-codes')
        if (!response.ok) {
          throw new Error('Failed to fetch store codes')
        }

        const data = await response.json()
        setStoreCodes(data)
      } catch (err) {
        console.error('Error fetching store codes:', err)
        setError(err instanceof Error ? err.message : 'Failed to fetch store codes')
      } finally {
        setIsLoading(false)
      }
    }

    fetchStoreCodes()
  }, [])

  // Filter stores that have orders to buy
  const storesWithOrders = storeCodes.filter(store => store._count.orders > 0)
  const ordersToFetch = storesWithOrders.reduce((acc: number, store: StoreCodeWithCount) => acc + store._count.orders, 0)

  if (error) {
    return (
      <ProtectedRoute requiredPermission={{ resource: 'orders', action: 'read' }}>
        <SimplePageWrapper title="Buy List">
          <Card className="p-6">
            <div className="text-center">
              <h2 className="text-base font-medium text-red-600">Error</h2>
              <p className="text-sm text-muted-foreground mt-2">{error}</p>
              <button
                onClick={() => window.location.reload()}
                className="mt-4 px-4 py-2 bg-primary text-primary-foreground rounded-md hover:bg-primary/90 transition-colors"
              >
                Try Again
              </button>
            </div>
          </Card>
        </SimplePageWrapper>
      </ProtectedRoute>
    )
  }

  return (
    <ProtectedRoute requiredPermission={{ resource: 'orders', action: 'read' }}>
      <SimplePageWrapper title="Buy List">

      {isLoading ? (
        <div className="grid gap-4 grid-cols-1 sm:grid-cols-2 lg:gap-6">
          {[...Array(4)].map((_, i) => (
            <Card key={i} className="p-4 h-20 flex items-center">
              <div className="animate-pulse w-full">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3 flex-1">
                    <div className="flex-1">
                      <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                      <div className="h-3 bg-gray-200 rounded w-1/2 mt-2"></div>
                    </div>
                  </div>
                  <div className="h-5 w-5 bg-gray-200 rounded flex-shrink-0"></div>
                </div>
              </div>
            </Card>
          ))}
        </div>
      ) : storesWithOrders.length === 0 ? (
        <Card className="p-6">
          <div className="text-center">
            <h2 className="text-base font-medium">No orders to buy</h2>
            <p className="text-sm text-muted-foreground mt-2">
              {storeCodes.length === 0
                ? "Add some store codes to get started with your buy list."
                : "All orders have been purchased! Add new orders to continue shopping."
              }
            </p>
            <p className="text-xs text-muted-foreground mt-3">Use the + button below to add orders.</p>
          </div>
        </Card>
      ) : (
        <div className="grid gap-4 grid-cols-1 sm:grid-cols-2 lg:gap-6">
          {/* All items card */}
          <Link href="/buy-list/all" className="block">
            <Card className="p-4 h-20 flex items-center hover:shadow-md hover:bg-accent/50 transition-all duration-200 cursor-pointer focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2">
              <div className="flex items-center justify-between w-full">
                <div className="flex-1 min-w-0">
                  <h3 className="font-medium text-base">All Stores</h3>
                  <div className="flex items-center gap-2 mt-1">
                    <p className="text-2xl font-bold text-primary">{ordersToFetch}</p>
                    <p className="text-sm text-muted-foreground">orders to buy</p>
                  </div>
                </div>
                <div className="text-muted-foreground ml-4" aria-hidden="true">
                  <svg className="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                  </svg>
                </div>
              </div>
            </Card>
          </Link>

          {/* Store code cards */}
          {storesWithOrders.map((storeCode) => (
            <Link key={storeCode.id} href={`/buy-list/${storeCode.code.toLowerCase()}`} className="block">
              <Card className="p-4 h-20 flex items-center hover:shadow-md hover:bg-accent/50 transition-all duration-200 cursor-pointer focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2">
                <div className="flex items-center justify-between w-full">
                  <div className="flex-1 min-w-0">
                    <h3 className="font-medium text-base truncate" title={storeCode.name || storeCode.code}>
                      {storeCode.name || storeCode.code}
                    </h3>
                    <div className="flex items-center gap-2 mt-1">
                      <p className="text-2xl font-bold text-blue-600">{storeCode._count.orders}</p>
                      <p className="text-sm text-muted-foreground">
                        {storeCode._count.orders === 1 ? 'order' : 'orders'}
                      </p>
                    </div>
                  </div>
                  <div className="text-muted-foreground ml-4" aria-hidden="true">
                    <svg className="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                    </svg>
                  </div>
                </div>
              </Card>
            </Link>
          ))}
        </div>
      )}
    </SimplePageWrapper>
    </ProtectedRoute>
  )
}

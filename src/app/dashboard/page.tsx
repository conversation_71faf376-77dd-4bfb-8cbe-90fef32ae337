'use client'

import { ProtectedRoute } from '@/components/auth/protected-route'
import { useAuth } from '@/contexts/auth-context'
import { LogOut, User, Shield, Settings } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { SimplePageWrapper } from '@/components/layout/page-wrapper'

function DashboardContent() {
  const { user, logout, hasRole, hasPermission } = useAuth()

  const handleLogout = async () => {
    await logout()
  }

  return (
    <SimplePageWrapper
      title="Dashboard"
      actions={
        <div className="flex items-center space-x-4">
          <span className="text-sm text-muted-foreground">
            Welcome, {user?.firstName || user?.email}
          </span>
          <Button
            onClick={handleLogout}
            variant="destructive"
            size="sm"
          >
            <LogOut className="h-4 w-4 mr-2" />
            Logout
          </Button>
        </div>
      }
    >

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {/* User Info Card */}
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <User className="h-6 w-6 text-muted-foreground" />
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-muted-foreground truncate">
                    User Information
                  </dt>
                  <dd className="text-lg font-medium text-foreground">
                    {user?.firstName} {user?.lastName}
                  </dd>
                  <dd className="text-sm text-muted-foreground">
                    {user?.email}
                  </dd>
                  <dd className="text-sm">
                    {user?.emailVerified ? (
                      <Badge variant="default">Email Verified</Badge>
                    ) : (
                      <Badge variant="destructive">Email Not Verified</Badge>
                    )}
                  </dd>
                </dl>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Roles Card */}
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <Shield className="h-6 w-6 text-muted-foreground" />
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-muted-foreground truncate">
                    Your Roles
                  </dt>
                  <dd className="mt-1 flex flex-wrap gap-1">
                    {user?.roles.map((role) => (
                      <Badge
                        key={role.id}
                        variant="secondary"
                      >
                        {role.displayName}
                      </Badge>
                    ))}
                  </dd>
                </dl>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Quick Actions Card */}
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <Settings className="h-6 w-6 text-muted-foreground" />
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-muted-foreground truncate">
                    Quick Actions
                  </dt>
                  <dd className="mt-2 space-y-2">
                    {hasPermission('orders', 'create') && (
                      <Button variant="ghost" className="w-full justify-start">
                        Create Order
                      </Button>
                    )}
                    {hasPermission('customers', 'create') && (
                      <Button variant="ghost" className="w-full justify-start">
                        Add Customer
                      </Button>
                    )}
                    {hasRole('admin') && (
                      <Button variant="ghost" className="w-full justify-start text-destructive hover:text-destructive">
                        Admin Panel
                      </Button>
                    )}
                  </dd>
                </dl>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Permissions Debug Info */}
      <Card className="mt-8">
        <CardHeader>
          <CardTitle>Your Permissions</CardTitle>
          <CardDescription>Detailed view of your role-based permissions</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {user?.roles.map((role) => (
              <Card key={role.id} className="border">
                <CardHeader className="pb-3">
                  <CardTitle className="text-base">{role.displayName}</CardTitle>
                </CardHeader>
                <CardContent className="pt-0">
                  <div className="space-y-1">
                    {role.permissions.map((permission) => (
                      <div key={permission.id} className="text-sm text-muted-foreground">
                        {permission.displayName}
                        <span className="text-xs text-muted-foreground/70 ml-1">
                          ({permission.resource}.{permission.action})
                        </span>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </CardContent>
      </Card>
    </SimplePageWrapper>
  )
}

export default function DashboardPage() {
  return (
    <ProtectedRoute>
      <DashboardContent />
    </ProtectedRoute>
  )
}

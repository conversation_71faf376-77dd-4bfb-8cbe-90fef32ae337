const { PrismaClient } = require('@prisma/client')

const prisma = new PrismaClient()

async function checkUsers() {
  try {
    console.log('👥 Checking users in database...\n')
    
    const users = await prisma.user.findMany({
      select: {
        id: true,
        email: true,
        username: true,
        passwordHash: true,
        isActive: true,
        emailVerified: true,
        createdAt: true
      }
    })
    
    console.log(`Found ${users.length} users:`)
    users.forEach(user => {
      console.log(`\n📧 Email: ${user.email}`)
      console.log(`🆔 ID: ${user.id}`)
      console.log(`👤 Username: ${user.username || 'None'}`)
      console.log(`🔐 Password Hash: ${user.passwordHash.substring(0, 20)}...`)
      console.log(`✅ Active: ${user.isActive}`)
      console.log(`📧 Verified: ${user.emailVerified}`)
      console.log(`📅 Created: ${user.createdAt}`)
    })
    
  } catch (error) {
    console.error('❌ Error:', error)
  } finally {
    await prisma.$disconnect()
  }
}

checkUsers()

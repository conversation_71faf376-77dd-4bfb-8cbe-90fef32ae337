/**
 * Simple test to verify login functionality
 */

const BASE_URL = 'http://localhost:3001'

async function testLogin() {
  console.log('🔐 Testing Login Functionality...\n')

  try {
    // Test with admin credentials
    console.log('Testing admin login...')
    const loginResponse = await fetch(`${BASE_URL}/api/auth/login`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        email: '<EMAIL>',
        password: 'password',
        rememberMe: false
      })
    })

    const loginData = await loginResponse.json()
    console.log('Login response status:', loginResponse.status)
    console.log('Login response:', {
      success: loginData.success,
      message: loginData.message,
      hasToken: !!loginData.token,
      hasUser: !!loginData.user,
      userEmail: loginData.user?.email,
      userRoles: loginData.user?.roles?.map(r => r.name)
    })

    if (loginResponse.status === 200 && loginData.success) {
      console.log('✅ Login API is working correctly!')
      console.log('✅ Token received:', loginData.token ? 'Yes' : 'No')
      console.log('✅ User data received:', loginData.user ? 'Yes' : 'No')
    } else {
      console.log('❌ Login failed')
      console.log('Error:', loginData.error || loginData.message)
    }

  } catch (error) {
    console.error('❌ Test failed:', error.message)
  }
}

testLogin()

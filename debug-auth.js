const { PrismaClient } = require('@prisma/client')

const prisma = new PrismaClient()

async function debugAuth() {
  try {
    console.log('🔍 Debugging Authentication Issues...\n')
    
    // Check if users exist
    const userCount = await prisma.user.count()
    console.log(`📊 Total users in database: ${userCount}`)
    
    if (userCount > 0) {
      const users = await prisma.user.findMany({
        select: {
          id: true,
          email: true,
          username: true,
          isActive: true,
          emailVerified: true,
          createdAt: true,
          roles: {
            include: {
              role: {
                include: {
                  permissions: {
                    include: {
                      permission: true
                    }
                  }
                }
              }
            }
          }
        }
      })
      
      console.log('\n👥 Users:')
      users.forEach(user => {
        console.log(`  - ${user.email} (ID: ${user.id})`)
        console.log(`    Active: ${user.isActive}, Verified: ${user.emailVerified}`)
        console.log(`    Roles: ${user.roles.map(ur => ur.role.name).join(', ') || 'None'}`)
        console.log('')
      })
    }
    
    // Check sessions
    const sessionCount = await prisma.userSession.count()
    console.log(`🔐 Total sessions: ${sessionCount}`)
    
    if (sessionCount > 0) {
      const activeSessions = await prisma.userSession.findMany({
        where: { isActive: true },
        include: {
          user: {
            select: {
              email: true
            }
          }
        }
      })
      
      console.log(`\n🟢 Active sessions: ${activeSessions.length}`)
      activeSessions.forEach(session => {
        console.log(`  - User: ${session.user.email}`)
        console.log(`    Expires: ${session.expiresAt}`)
        console.log(`    Token: ${session.token.substring(0, 20)}...`)
        console.log('')
      })
    }
    
    // Check roles and permissions
    const roleCount = await prisma.role.count()
    console.log(`🎭 Total roles: ${roleCount}`)
    
    if (roleCount > 0) {
      const roles = await prisma.role.findMany({
        include: {
          permissions: {
            include: {
              permission: true
            }
          }
        }
      })
      
      console.log('\n📋 Roles:')
      roles.forEach(role => {
        console.log(`  - ${role.name} (${role.displayName})`)
        console.log(`    Permissions: ${role.permissions.length}`)
      })
    }
    
  } catch (error) {
    console.error('❌ Error debugging auth:', error)
  } finally {
    await prisma.$disconnect()
  }
}

debugAuth()

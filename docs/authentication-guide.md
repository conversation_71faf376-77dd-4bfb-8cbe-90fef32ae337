# PasaBuy Local - Authentication & Authorization Guide

## 🔐 Overview

PasaBuy Local now includes a comprehensive authentication and authorization system with role-based access control (RBAC), JWT tokens, and multi-user data isolation.

## 🚀 Quick Start

### Default Admin Account
- **Email**: `<EMAIL>`
- **Password**: `password`
- **Role**: Administrator (full access)

### User Registration
Users can register at `/auth/register` with:
- Email address
- Username (optional)
- First and last name
- Strong password (8+ chars, uppercase, lowercase, number, special char)
- Phone number (optional)

### User Roles
1. **Admin**: Full system access, user management
2. **Seller**: Can manage orders, customers, and invoices
3. **Buyer**: Can create and view orders and customers

## 🔧 API Authentication

### Getting a Token
```bash
POST /api/auth/login
{
  "email": "<EMAIL>",
  "password": "password",
  "rememberMe": false
}
```

### Using the Token
Include in Authorization header:
```bash
Authorization: Bearer <your-jwt-token>
```

### Protected Endpoints
All API endpoints now require authentication:
- `/api/orders/*` - Requires `orders` permissions
- `/api/customers/*` - Requires `customers` permissions
- `/api/admin/*` - Requires admin role

## 🛡️ Security Features

### Password Security
- Bcrypt hashing with 12 salt rounds
- Password strength requirements
- Account lockout after 5 failed attempts (15 minutes)

### JWT Tokens
- 7-day expiration
- Refresh token support (30 days)
- Secure HTTP-only cookies for refresh tokens
- Session invalidation on logout

### Rate Limiting
- Registration: 5 attempts per 15 minutes per IP
- Login: 10 attempts per 15 minutes per IP
- Password reset: 3 attempts per 15 minutes per IP
- API endpoints: 100 requests per minute per user

### Data Isolation
- Users can only access their own data
- Row-level security implemented
- Multi-tenant architecture

## 📱 Frontend Integration

### Authentication Context
```jsx
import { useAuth } from '@/contexts/auth-context'

function MyComponent() {
  const { user, login, logout, hasPermission, hasRole } = useAuth()
  
  if (!user) {
    return <LoginForm />
  }
  
  return (
    <div>
      <h1>Welcome, {user.firstName}!</h1>
      {hasRole('admin') && <AdminPanel />}
      {hasPermission('orders', 'create') && <CreateOrderButton />}
    </div>
  )
}
```

### Protected Routes
```jsx
import { ProtectedRoute } from '@/components/auth/protected-route'

function AdminPage() {
  return (
    <ProtectedRoute requiredRole="admin">
      <AdminDashboard />
    </ProtectedRoute>
  )
}
```

## 🔑 Permissions System

### Resources
- `users` - User management
- `orders` - Order management
- `customers` - Customer management
- `invoices` - Invoice management
- `stores` - Store management

### Actions
- `create` - Create new records
- `read` - View records
- `update` - Modify records
- `delete` - Remove records

### Role Permissions
**Admin Role:**
- All permissions on all resources

**Seller Role:**
- `orders.*` (except delete)
- `customers.*` (except delete)
- `invoices.*` (except delete)
- `stores.*` (except delete)

**Buyer Role:**
- `orders.create`, `orders.read`, `orders.update`
- `customers.create`, `customers.read`, `customers.update`

## 🛠️ Admin Dashboard

Access the admin dashboard at `/admin` (admin role required):

### User Management
- View all users with pagination and search
- Filter by role and status
- Assign/remove roles
- Enable/disable accounts
- View user activity and permissions

### Features
- Real-time user statistics
- Role-based filtering
- Bulk operations
- Audit logging

## 🧪 Testing

Run the comprehensive test suite:
```bash
node scripts/test-auth.js
```

Tests cover:
- User registration and login
- Token validation
- Permission checking
- Data isolation
- Admin functionality
- Session management

## 🔒 Security Best Practices

### Environment Variables
Set these in production:
```env
JWT_SECRET="your-very-long-random-secret-key"
DATABASE_URL="your-production-database-url"
```

### Production Checklist
- [ ] Change default admin password
- [ ] Set strong JWT secret
- [ ] Configure HTTPS
- [ ] Set up email service for verification
- [ ] Enable audit logging
- [ ] Configure rate limiting
- [ ] Set up monitoring

## 📚 API Reference

### Authentication Endpoints
- `POST /api/auth/register` - Register new user
- `POST /api/auth/login` - Login user
- `POST /api/auth/logout` - Logout user
- `GET /api/auth/me` - Get current user
- `POST /api/auth/refresh` - Refresh token
- `POST /api/auth/reset-password` - Request password reset
- `PUT /api/auth/reset-password` - Reset password with token
- `POST /api/auth/verify-email` - Verify email address

### Admin Endpoints
- `GET /api/admin/users` - List all users
- `POST /api/admin/users` - Create new user
- `GET /api/admin/users/:id` - Get user details
- `PUT /api/admin/users/:id` - Update user
- `DELETE /api/admin/users/:id` - Deactivate user
- `GET /api/admin/roles` - List all roles

## 🐛 Troubleshooting

### Common Issues

**"Authentication required" error:**
- Ensure JWT token is included in Authorization header
- Check token hasn't expired
- Verify user account is active

**"Insufficient permissions" error:**
- Check user has required role/permission
- Verify role assignments in admin dashboard
- Ensure user account is active

**Login failures:**
- Check account isn't locked (wait 15 minutes)
- Verify email and password are correct
- Ensure account is active

### Debug Mode
Set `NODE_ENV=development` for detailed error messages.

## 📞 Support

For issues or questions:
1. Check the troubleshooting section
2. Review the test scripts for examples
3. Check the admin dashboard for user status
4. Review audit logs for security events
